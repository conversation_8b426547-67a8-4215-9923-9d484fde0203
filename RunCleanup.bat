@echo off
echo System Cleanup Script Launcher
echo ==============================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges...
    echo.
) else (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Please right-click on this batch file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Set PowerShell execution policy temporarily
echo Setting PowerShell execution policy...
powershell -Command "Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force"

REM Run the PowerShell script
echo Starting System Cleanup...
echo.
powershell -ExecutionPolicy Bypass -File "%~dp0SystemCleanup.ps1"

REM Check if script ran successfully
if %errorLevel% == 0 (
    echo.
    echo System cleanup completed successfully!
) else (
    echo.
    echo System cleanup encountered an error. Check the log file for details.
)

echo.
pause
