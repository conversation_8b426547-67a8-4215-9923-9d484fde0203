# Professional System Cleanup Script for Windows
# Monitors system resources and performs cleanup when RAM > 85% or CPU > 90%
# Version 1.0

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run as Administrator." -ForegroundColor Red
    Write-Host "Right-click on PowerShell and select 'Run as Administrator', then run this script again." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Initialize logging
$LogPath = "$env:USERPROFILE\Desktop\SystemCleanup_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$StartTime = Get-Date

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    Add-Content -Path $LogPath -Value $LogEntry
    Write-Host $LogEntry -ForegroundColor $(if($Level -eq "ERROR"){"Red"}elseif($Level -eq "WARN"){"Yellow"}else{"Green"})
}

function Get-SystemResources {
    try {
        $RAM = Get-CimInstance -ClassName Win32_OperatingSystem
        $CPU = Get-CimInstance -ClassName Win32_Processor | Measure-Object -Property LoadPercentage -Average
        
        $RAMUsedPercent = [math]::Round((($RAM.TotalVisibleMemorySize - $RAM.FreePhysicalMemory) / $RAM.TotalVisibleMemorySize) * 100, 2)
        $CPUUsedPercent = [math]::Round($CPU.Average, 2)
        
        return @{
            RAMPercent = $RAMUsedPercent
            CPUPercent = $CPUUsedPercent
        }
    }
    catch {
        Write-Log "Error getting system resources: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Get-SystemProcesses {
    # Core Windows processes that should never be killed
    $SystemProcesses = @(
        'System', 'Registry', 'smss', 'csrss', 'wininit', 'winlogon', 'services', 'lsass', 'lsm',
        'svchost', 'spoolsv', 'explorer', 'dwm', 'audiodg', 'conhost', 'dllhost', 'rundll32',
        'taskhostw', 'SearchIndexer', 'WmiPrvSE', 'msdtc', 'VBoxService', 'vmtoolsd'
    )
    return $SystemProcesses
}

function Stop-HighResourceProcesses {
    $SystemProcesses = Get-SystemProcesses
    $KilledCount = 0
    
    try {
        # Get processes using high CPU or Memory (top 20% resource consumers)
        $Processes = Get-Process | Where-Object { 
            $_.ProcessName -notin $SystemProcesses -and
            $_.Path -and
            $_.Path -notlike "*\Windows\System32\*" -and
            $_.Path -notlike "*\Windows\SysWOW64\*" -and
            ($_.CPU -gt 10 -or $_.WorkingSet -gt 100MB)
        } | Sort-Object @{Expression={$_.CPU + ($_.WorkingSet/1MB)}} -Descending | Select-Object -First 15
        
        foreach ($Process in $Processes) {
            try {
                $MemoryMB = [math]::Round($Process.WorkingSet / 1MB, 2)
                $CPUTime = [math]::Round($Process.CPU, 2)
                
                Stop-Process -Id $Process.Id -Force -ErrorAction Stop
                Write-Log "Killed process: $($Process.ProcessName) (PID: $($Process.Id), Memory: ${MemoryMB}MB, CPU: ${CPUTime}s)"
                $KilledCount++
                Start-Sleep -Milliseconds 100
            }
            catch {
                Write-Log "Failed to kill process $($Process.ProcessName): $($_.Exception.Message)" "WARN"
            }
        }
    }
    catch {
        Write-Log "Error in Stop-HighResourceProcesses: $($_.Exception.Message)" "ERROR"
    }
    
    Write-Log "Killed $KilledCount high-resource processes"
}

function Stop-NonMicrosoftServices {
    $StoppedCount = 0
    
    try {
        $Services = Get-WmiObject -Class Win32_Service | Where-Object {
            $_.State -eq "Running" -and
            $_.PathName -and
            $_.PathName -notlike "*\Windows\System32\*" -and
            $_.PathName -notlike "*\Windows\SysWOW64\*" -and
            $_.PathName -notlike "*Microsoft*" -and
            $_.StartMode -ne "Disabled"
        }
        
        foreach ($Service in $Services) {
            try {
                Stop-Service -Name $Service.Name -Force -ErrorAction Stop
                Write-Log "Stopped service: $($Service.Name) - $($Service.DisplayName)"
                $StoppedCount++
            }
            catch {
                Write-Log "Failed to stop service $($Service.Name): $($_.Exception.Message)" "WARN"
            }
        }
    }
    catch {
        Write-Log "Error in Stop-NonMicrosoftServices: $($_.Exception.Message)" "ERROR"
    }
    
    Write-Log "Stopped $StoppedCount non-Microsoft services"
}

function Disable-StartupPrograms {
    $DisabledCount = 0
    
    # Registry startup locations
    $StartupKeys = @(
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce",
        "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
        "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
    )
    
    # Disable registry startup entries
    foreach ($Key in $StartupKeys) {
        try {
            if (Test-Path $Key) {
                $Items = Get-ItemProperty -Path $Key -ErrorAction SilentlyContinue
                if ($Items) {
                    $Items.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
                        try {
                            Remove-ItemProperty -Path $Key -Name $_.Name -Force -ErrorAction Stop
                            Write-Log "Disabled startup entry: $($_.Name) from $Key"
                            $DisabledCount++
                        }
                        catch {
                            Write-Log "Failed to remove startup entry $($_.Name): $($_.Exception.Message)" "WARN"
                        }
                    }
                }
            }
        }
        catch {
            Write-Log "Error processing registry key $Key: $($_.Exception.Message)" "WARN"
        }
    }
    
    # Disable scheduled tasks (non-Microsoft)
    try {
        $Tasks = Get-ScheduledTask | Where-Object {
            $_.State -eq "Ready" -and
            $_.Author -notlike "*Microsoft*" -and
            $_.TaskPath -notlike "*Microsoft*"
        }
        
        foreach ($Task in $Tasks) {
            try {
                Disable-ScheduledTask -TaskName $Task.TaskName -TaskPath $Task.TaskPath -ErrorAction Stop
                Write-Log "Disabled scheduled task: $($Task.TaskName)"
                $DisabledCount++
            }
            catch {
                Write-Log "Failed to disable task $($Task.TaskName): $($_.Exception.Message)" "WARN"
            }
        }
    }
    catch {
        Write-Log "Error processing scheduled tasks: $($_.Exception.Message)" "ERROR"
    }
    
    Write-Log "Disabled $DisabledCount startup programs and tasks"
}

# Main execution
Write-Log "=== System Cleanup Script Started ==="
Write-Log "Script version: 1.0"
Write-Log "Running with administrative privileges: $([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)"

# Check system resources
$Resources = Get-SystemResources
if (-not $Resources) {
    Write-Log "Failed to get system resources. Exiting." "ERROR"
    exit 1
}

Write-Log "Current system usage - RAM: $($Resources.RAMPercent)%, CPU: $($Resources.CPUPercent)%"

# Check if cleanup is needed
if ($Resources.RAMPercent -gt 85 -or $Resources.CPUPercent -gt 90) {
    Write-Log "High resource usage detected. Starting cleanup process..."
    
    # Perform cleanup operations
    Write-Log "Phase 1: Stopping high-resource processes..."
    Stop-HighResourceProcesses
    
    Write-Log "Phase 2: Stopping non-Microsoft services..."
    Stop-NonMicrosoftServices
    
    Write-Log "Phase 3: Disabling startup programs..."
    Disable-StartupPrograms
    
    # Final resource check
    Start-Sleep -Seconds 3
    $FinalResources = Get-SystemResources
    if ($FinalResources) {
        Write-Log "Final system usage - RAM: $($FinalResources.RAMPercent)%, CPU: $($FinalResources.CPUPercent)%"
        $RAMReduction = [math]::Round($Resources.RAMPercent - $FinalResources.RAMPercent, 2)
        $CPUReduction = [math]::Round($Resources.CPUPercent - $FinalResources.CPUPercent, 2)
        Write-Log "Resource reduction - RAM: ${RAMReduction}%, CPU: ${CPUReduction}%"
    }
    
    Write-Log "=== Cleanup completed successfully ==="
} else {
    Write-Log "System resources are within acceptable limits. No cleanup needed."
    Write-Log "Thresholds: RAM > 85%, CPU > 90%"
}

$Duration = (Get-Date) - $StartTime
Write-Log "Script execution time: $($Duration.TotalSeconds) seconds"
Write-Log "Log file saved to: $LogPath"
Write-Log "=== Script finished ==="
